// DODO was here
import { PureComponent } from 'react';
import { t } from '@superset-ui/core';

import PopoverDropdown, {
  OnChangeHandler,
} from 'src/components/PopoverDropdown';

interface MarkdownModeDropdownProps {
  id: string;
  value: string;
  onChange: OnChangeHandler;
}

// DODO changed 53165180
const dropdownOptions = [
  {
    value: `edit_en`,
    label: `${t('Edit')} (EN)`,
  },
  {
    value: `preview_en`,
    label: `${t('Preview')} (EN)`,
  },
  {
    value: `edit_ru`,
    label: `${t('Edit')} (RU)`,
  },
  {
    value: `preview_ru`,
    label: `${t('Preview')} (RU)`,
  },
];

export default class MarkdownModeDropdown extends PureComponent<MarkdownModeDropdownProps> {
  render() {
    const { id, value, onChange } = this.props;

    return (
      <PopoverDropdown
        id={id}
        options={dropdownOptions}
        value={value}
        onChange={onChange}
      />
    );
  }
}
