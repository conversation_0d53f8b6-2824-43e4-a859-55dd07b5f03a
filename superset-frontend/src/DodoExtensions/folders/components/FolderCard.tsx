import { useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Tree, TreeNodeProps } from 'antd-v5';
import { styled, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Card from 'src/components/Card';
import Icons from 'src/components/Icons';
import { Input } from 'src/components/Input';
import { Entity, isFolderType } from './types';

const locale = bootstrapData?.common?.locale || 'en';

interface TreeNodeData {
  key: string;
  id: number;
  title: string;
  type: Entity['type'];
  is_certified?: boolean;
  children?: TreeNodeData[];
}

const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};

  .header-title {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;

    svg {
      color: ${({ theme }) => theme.colors.primary.base};
    }
  }

  .browse-link {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;
    line-height: 1rem;
    color: ${({ theme }) => theme.colors.primary.base} !important;

    a {
      color: ${({ theme }) => theme.colors.primary.base} !important;
    }

    span {
      height: 14px;
    }
  }

  .antd5-card-body {
    display: flex;
    flex-direction: column;
    padding: ${({ theme }) => theme.gridUnit * 6}px;
    flex: 1;
  }

  .tree-container {
    margin-top: ${({ theme }) => theme.gridUnit * 5}px;
    overflow-y: auto;
    max-height: 100%;
    flex: 1;
  }

  .tree {
    background-color: transparent;
  }

  .certified-icon {
    color: ${({ theme }) => theme.colors.primary.base};
  }
`;

const InputIconAlignment = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${({ theme }) => theme.colors.grayscale.base};
`;

const BrowseLink = ({ to }: { to: string }) => (
  <Link to={to} className="browse-link">
    <span>{t('Browse dashboards')}</span>
    <Icons.ArrowRightOutlined iconSize="m" />
  </Link>
);

interface IProps {
  title: string;
  to: string;
  canEdit: boolean;
  data: Entity[];
}

const FolderCard = ({ title, to, canEdit, data }: IProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const changeSearch: React.ChangeEventHandler<HTMLInputElement> = event =>
    setSearchTerm(event.target.value);

  const stopSearching = () => {
    setSearchTerm('');
    searchInputRef.current!.blur();
  };

  const titleNode = canEdit ? (
    <span className="header-title">
      <Link to="/dashboard/list/">
        <Icons.EditOutlined iconSize="l" />
      </Link>
      <span>{title}</span>
    </span>
  ) : (
    title
  );

  const buildTreeData = (entities: Entity[]): TreeNodeData[] =>
    entities.map(entity => ({
      key: `${entity.type}-${entity.id}`,
      id: entity.id,
      title: locale === 'ru' ? entity.title_ru : entity.title_en,
      type: entity.type,
      is_certified: isFolderType(entity) ? false : entity?.is_certified,
      children: isFolderType(entity)
        ? buildTreeData(entity.children || [])
        : [],
    }));

  const treeData = buildTreeData(data);

  const filterTreeData = (
    data: TreeNodeData[],
    searchTerm: string,
  ): TreeNodeData[] => {
    if (!searchTerm) return data;

    const matchesSearch = (node: TreeNodeData) =>
      node.title.toLowerCase().includes(searchTerm.toLowerCase());

    const filterNode = (node: TreeNodeData): TreeNodeData | null => {
      if (isFolderType(node) && matchesSearch(node)) {
        return {
          ...node,
        };
      }

      if (isFolderType(node)) {
        const filteredChildren = node.children
          ?.map(filterNode)
          .filter(Boolean) as TreeNodeData[];

        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren,
          };
        }
        return null;
      }

      if (matchesSearch(node)) {
        return node;
      }

      return null;
    };

    return data.map(filterNode).filter(Boolean) as TreeNodeData[];
  };

  const filteredTreeData = filterTreeData(treeData, searchTerm);

  return (
    <StyledCard title={titleNode} extra={<BrowseLink to={to} />}>
      <Input
        type="text"
        ref={searchInputRef as any}
        value={searchTerm}
        placeholder={t('Search')}
        onChange={changeSearch}
        prefix={
          <InputIconAlignment>
            <Icons.Search iconSize="m" />
          </InputIconAlignment>
        }
        suffix={
          <InputIconAlignment>
            {searchTerm && (
              <Icons.XLarge iconSize="m" onClick={stopSearching} />
            )}
          </InputIconAlignment>
        }
      />
      <div className="tree-container">
        <Tree
          treeData={filteredTreeData}
          titleRender={nodeData => {
            if (isFolderType(nodeData)) {
              return nodeData.title;
            }

            return (
              <Link
                to={`/superset/dashboard/${nodeData.id}`}
                style={{ color: 'inherit' }}
              >
                {nodeData.is_certified && (
                  <Icons.Certified className="certified-icon" iconSize="m" />
                )}
                {nodeData.title}
              </Link>
            );
          }}
          switcherIcon={<Icons.CaretDownOutlined />}
          icon={(props: TreeNodeProps & TreeNodeData) => {
            if (props.data.type === 'dashboard') {
              return <Icons.FundViewOutlined iconSize="m" />;
            }
            return props.expanded ? (
              <Icons.FolderOpenOutlined iconSize="m" />
            ) : (
              <Icons.FolderOutlined iconSize="m" />
            );
          }}
          className="tree"
          showLine
          showIcon
          defaultExpandAll
        />
      </div>
    </StyledCard>
  );
};

export default FolderCard;
