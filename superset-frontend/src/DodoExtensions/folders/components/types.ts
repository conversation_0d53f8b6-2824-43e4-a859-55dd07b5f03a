interface Folder {
  id: number;
  type: 'folder';
  title_ru: string;
  title_en: string;
  description_ru: string;
  description_en: string;
  parent: string;
  children: Entity[];
}

interface Dashboard {
  id: number;
  type: 'dashboard';
  title_ru: string;
  title_en: string;
  is_certified: boolean;
  parent: string;
}

export type Entity = Folder | Dashboard;

export interface FoldersData {
  global: Entity[];
  personal: Entity[];
  team?: Entity[];
}

export const isFolderType = (entity: unknown): entity is Folder =>
  entity !== null &&
  typeof entity === 'object' &&
  'type' in entity &&
  entity.type === 'folder';
