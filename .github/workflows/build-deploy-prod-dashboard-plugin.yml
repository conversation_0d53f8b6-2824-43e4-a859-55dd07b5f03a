name: Build and Deploy Dashboard Plugin [PROD]

on:
  workflow_dispatch:
    inputs:
      superset_ref:
        description: 'Superset frontend branch'
        required: true
        default: '4.1.1-dodo'
env:
  NPM_TOKEN: ${{ secrets.PACKAGES_TOKEN }}
  BUILD_NUMBER: ${{ github.run_number }}
  MICROFRONTEND_ENV_GITOPS: ${{ secrets.MICROFRONTEND_ENV_GITOPS }}
  ENTRY_POINT: supersetDashboardPlugin
  FRONTEND: officemanager
  ENVIRONMENT: we

jobs:
  build-plugin-prod:
    name: Build Superset Frontend
    runs-on: ubuntu-22.04
    outputs:
      supersetDashboardPlugin: ${{ steps.publish-artifact.outputs.supersetDashboardPlugin }}
    steps:

      - name: Print branches versions
        run: |
          echo "Superset version ${{ github.ref }}"

      - name: Checkout dodopizza/superset
        uses: actions/checkout@v4.1.7
        with:
          path: repos/superset

      - name: Extract VERSION from version.py
        id: extract-version
        working-directory: repos/superset
        run: |
          VERSION=$(python3 dodo_build/scripts/extract_version.py dodo_build/configs/version.py)
          echo "VERSION=${VERSION}" >> $GITHUB_ENV
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "Extracted VERSION: ${VERSION}"

      - name: Memory Check
        working-directory: repos/superset/docker
        run: |
          sh frontend-mem-nag.sh

      - name: Use Node.js 18.19.1
        uses: actions/setup-node@v3
        with:
          registry-url: 'https://npm.pkg.github.com'
          scope: '@dodopizza'
          node-version: 18.19.1

      - name: npm ci
        working-directory: repos/superset/superset-frontend
        run: |
          npm ci

      # - name: npm run lint
      #   working-directory: repos/superset/superset-frontend
      #   run: |
      #     npm run lint

      - name: Build superset-dashboard-plugin [PROD]
        working-directory: repos/superset/superset-frontend
        env:
          DODO_VERSION: ${{ env.VERSION }}
        run: |
          npm run pl:build:prod
          echo "Build success"

      - name: publish artifact
        id: publish-artifact
        uses: dodopizza/create-micro-frontend-app/actions/publish@7.0.1
        with:
          artifact-dictionary: |
            ${{ env.ENTRY_POINT }} : ./repos/superset/superset-frontend/public

  deploy-prod:
    name: Deploy prod artifact
    needs: [ build-plugin-prod ]
    runs-on: ubuntu-latest
    steps:
      - name: Deploy artifact ${{ needs.build-plugin-prod.outputs.supersetDashboardPlugin }} to ${{ env.ENVIRONMENT }}
        uses: dodopizza/create-micro-frontend-app/actions/deploy@7.0.1
        with:
          microfrontend-env-gitops: ${{ env.MICROFRONTEND_ENV_GITOPS }}
          deploy-dictionary: |
            ${{ env.ENVIRONMENT }} : ${{ env.FRONTEND }} : ${{ env.ENTRY_POINT }} : ${{ needs.build-plugin-prod.outputs.supersetDashboardPlugin }}
